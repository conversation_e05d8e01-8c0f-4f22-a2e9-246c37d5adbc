<div class="call-us-module">

<div class="nav-bar nav-medium-up">

        <p id="nav-medium-call">
            <?php echo $this->element('phone_number', array('class' => 'quote-module__request')) ?>
        </p>

        <p id="nav-medium-email">
            <?php echo $html->link('Email an Expert <b>Now</b>', 'mailto:<EMAIL>', array('class' => 'quote-module__request'), false, false); ?>
        </p>

        <p id="nav-medium-enquire">
            <?php echo $html->link('Make an <b>Enquiry</b>', '/make_an_enquiry', array('class' => 'quote-module__request'), false, false); ?>
        </p>

        <p id="nav-medium-newsletter">
            <?php echo $html->link('Newsletter <b>sign up</b>', '/subscriptions', array('class' => 'quote-module__request'), false, false); ?>
        </p>
</div>
<div class="nav-bar nav-small">
    <ul>
        <li id="nav-small-call"><a href="tel:08003163012"><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M28.8646 21.8218L24.34 21.3052C23.8079 21.2427 23.2687 21.3016 22.7627 21.4775C22.2567 21.6534 21.7971 21.9416 21.4185 22.3206L18.1408 25.5983C13.0842 23.0259 8.97404 18.9157 6.40164 13.8591L9.69716 10.5635C10.4631 9.79755 10.8372 8.72873 10.7125 7.64209L10.1959 3.15303C10.0953 2.28388 9.67839 1.48215 9.02463 0.900654C8.37086 0.319161 7.52598 -0.00142522 6.65103 4.76331e-06H3.56927C1.55633 4.76331e-06 -0.118152 1.67449 0.0065438 3.68745C0.950666 18.9004 13.1174 31.0493 28.3124 31.9935C30.3253 32.1182 31.9998 30.4437 31.9998 28.4307V25.3489C32.0176 23.5498 30.6638 22.0356 28.8646 21.8218Z" fill="currentColor"/>
</svg>
<span>Call</span></a></li>
        <li id="nav-small-enquire"><a href="/make_an_enquiry"><svg width="31" height="33" viewBox="0 0 31 33" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3.1 31.4286C2.2475 31.4286 1.51797 31.1211 0.9114 30.5061C0.304833 29.8912 0.00103333 29.151 0 28.2857V6.28572C0 5.42143 0.3038 4.68181 0.9114 4.06686C1.519 3.45191 2.24853 3.14391 3.1 3.14286H4.65V1.57143C4.65 1.1262 4.7988 0.753244 5.0964 0.452577C5.394 0.15191 5.76187 0.00105304 6.2 5.41871e-06C6.63813 -0.0010422 7.00651 0.149815 7.30515 0.452577C7.60378 0.755339 7.75206 1.12829 7.75 1.57143V3.14286H20.15V1.57143C20.15 1.1262 20.2988 0.753244 20.5964 0.452577C20.894 0.15191 21.2619 0.00105304 21.7 5.41871e-06C22.1381 -0.0010422 22.5065 0.149815 22.8051 0.452577C23.1038 0.755339 23.2521 1.12829 23.25 1.57143V3.14286H24.8C25.6525 3.14286 26.3825 3.45086 26.9901 4.06686C27.5977 4.68286 27.901 5.42248 27.9 6.28572V13.6321C27.9 14.0774 27.7512 14.4509 27.4536 14.7526C27.156 15.0543 26.7881 15.2046 26.35 15.2036C25.9119 15.2025 25.544 15.0517 25.2464 14.751C24.9488 14.4503 24.8 14.0774 24.8 13.6321V12.5714H3.1V28.2857H12.09C12.5292 28.2857 12.8975 28.4366 13.1951 28.7383C13.4927 29.04 13.641 29.413 13.64 29.8571C13.639 30.3013 13.4902 30.6748 13.1936 30.9776C12.897 31.2803 12.5292 31.4307 12.09 31.4286H3.1ZM23.25 33C21.1058 33 19.2784 32.2337 17.7676 30.701C16.2569 29.1683 15.501 27.3156 15.5 25.1429C15.499 22.9701 16.2548 21.1174 17.7676 19.5847C19.2804 18.052 21.1079 17.2857 23.25 17.2857C25.3921 17.2857 27.2201 18.052 28.7339 19.5847C30.2477 21.1174 31.0031 22.9701 31 25.1429C30.9969 27.3156 30.241 29.1689 28.7323 30.7026C27.2237 32.2363 25.3962 33.0021 23.25 33ZM24.025 24.8286V21.2143C24.025 21.0048 23.9475 20.8214 23.7925 20.6643C23.6375 20.5071 23.4567 20.4286 23.25 20.4286C23.0433 20.4286 22.8625 20.5071 22.7075 20.6643C22.5525 20.8214 22.475 21.0048 22.475 21.2143V24.7893C22.475 24.9988 22.5137 25.202 22.5912 25.399C22.6687 25.596 22.785 25.7725 22.94 25.9286L25.3037 28.325C25.4587 28.4821 25.6396 28.5607 25.8462 28.5607C26.0529 28.5607 26.2337 28.4821 26.3887 28.325C26.5437 28.1679 26.6212 27.9845 26.6212 27.775C26.6212 27.5655 26.5437 27.3821 26.3887 27.225L24.025 24.8286Z" fill="currentColor"/>
</svg>
<span>Enquire</span></a></li>
        <li id="nav-small-chat" class="disabled" style="display: none;"><a href="#"><svg width="34" height="33" viewBox="0 0 34 33" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M34 10.56C34 9.85983 33.7244 9.18834 33.234 8.69324C32.7435 8.19814 32.0783 7.92 31.3846 7.92H26.1538V2.64C26.1538 1.93983 25.8783 1.26833 25.3878 0.773238C24.8973 0.278142 24.2321 0 23.5385 0H2.61538C1.92174 0 1.25651 0.278142 0.766028 0.773238C0.275549 1.26833 0 1.93983 0 2.64V23.76C0.0007678 24.0083 0.0709214 24.2514 0.202401 24.4613C0.333881 24.6712 0.521354 24.8394 0.743282 24.9466C0.96521 25.0538 1.21259 25.0956 1.457 25.0672C1.70142 25.0389 1.93295 24.9415 2.125 24.7863L7.84615 20.13V25.08C7.84615 25.7802 8.1217 26.4517 8.61218 26.9468C9.10266 27.4419 9.76789 27.72 10.4615 27.72H25.7599L31.875 32.7063C32.1064 32.8952 32.3947 32.9988 32.6923 33C33.0391 33 33.3717 32.8609 33.617 32.6134C33.8622 32.3658 34 32.0301 34 31.68V10.56ZM27.0447 25.3737C26.8133 25.1848 26.525 25.0812 26.2274 25.08H10.4615V19.8H23.5385C24.2321 19.8 24.8973 19.5219 25.3878 19.0268C25.8783 18.5317 26.1538 17.8602 26.1538 17.16V10.56H31.3846V28.9163L27.0447 25.3737Z" fill="currentColor"/>
</svg>
<span>Chat</span></a></li>
        <li id="nav-small-email"><a href="mailto:<EMAIL>"><svg xmlns="http://www.w3.org/2000/svg" width="32" height="32"><path fill="currentColor" d="M28.8 3.86H3.2C1.44 3.86.016 5.3.016 7.06L0 26.26c0 1.76 1.44 3.2 3.2 3.2h25.6c1.76 0 3.2-1.44 3.2-3.2V7.06c0-1.76-1.44-3.2-3.2-3.2zm-1.6 22.4H4.8c-.88 0-1.6-.72-1.6-1.6v-14.4l11.104 6.943a3.17 3.17 0 0 0 3.392 0L28.8 10.26v14.4c0 .88-.72 1.6-1.6 1.6zM16 15.06l-12.8-8h25.6z" style="stroke-width:1.6"/></svg>
<span>Email</span></a></li>
    </ul>
</div>
</div>
<script>
document.addEventListener("DOMContentLoaded", function () {

    var chatButton = document.querySelector('#nav-small-chat');
    var emailButton = document.querySelector('#nav-small-email');

    // Chat/Email button management functions
    function showChatButton() {
        console.log('[CHAT-STATUS] Operators online - showing chat button');
        if (chatButton) {
            chatButton.style.display = 'block';
            chatButton.classList.remove('disabled');
        }
        if (emailButton) {
            emailButton.style.display = 'none';
        }
    }

    function showEmailButton() {
        console.log('[CHAT-STATUS] Operators offline - showing email button');
        if (chatButton) {
            chatButton.style.display = 'none';
            chatButton.classList.add('disabled');
        }
        if (emailButton) {
            emailButton.style.display = 'block';
        }
    }

    // Initialize with email button visible (safe default)
    showEmailButton();

    // Zoho SalesIQ integration
    if (typeof $zoho !== 'undefined' && $zoho.salesiq) {
        $zoho.salesiq.ready = function() {
            console.log('[CHAT-STATUS] Zoho SalesIQ ready');

            // Check for operators coming online
            $zoho.salesiq.chat.online(function() {
                showChatButton();
            });

            // Check for operators going offline
            $zoho.salesiq.chat.offline(function() {
                showEmailButton();
            });
        }
    } else {
        console.log('[CHAT-STATUS] Zoho SalesIQ not available - using email fallback');
    }

    // Chat button click handler
    if (chatButton) {
        chatButton.addEventListener('click', function(e) {
            e.preventDefault();
            if (typeof $zoho !== 'undefined' && $zoho.salesiq && $zoho.salesiq.chat) {
                $zoho.salesiq.chat.start();
            }
        });
    }

    /* SCROLL FUNCTIONS */

    var mobileBarElement = document.querySelector('.mobile-bar');
    window.mobileBar = window.mobileBar || {};
    if (mobileBarElement !== null) {
        var mobileBarDisplay = window.getComputedStyle(mobileBarElement).display;
        window.mobileBar.status = (mobileBarDisplay == 'block' || mobileBarDisplay == 'flex') ? 'open' : 'closed';
    }

    /* Hide header on scroll down, show on scroll up */
    var didScroll,
        lastScrollTop = 0,
        delta = 5,
        windowInnerHeight = window.innerHeight,
        mobileBarHeight = mobileBarElement ? mobileBarElement.offsetHeight : 0;

    window.addEventListener('scroll', function () {
        didScroll = true;
    });

    setInterval(function () {
        if (didScroll) {
            hasScrolled();
            didScroll = false;
        }
    }, 250);

    function hasScrolled() {
        var st = window.scrollY;

        if (st > lastScrollTop) {
            // Scroll Down
            // Show bar
            if (mobileBarElement && !mobileBarElement.classList.contains('mobile-bar-show')) {
                    mobileBarElement.classList.add('mobile-bar-show');
                    mobileBarElement.classList.remove('mobile-bar-hide');
                    const showEvent = new Event('mobilebar:show');
                    window.dispatchEvent(showEvent);
                    window.mobileBar.status = 'open';
            }
        } else {
            // Scroll Up
            mobileBarElement.classList.remove('mobile-bar-show');
            mobileBarElement.classList.add('mobile-bar-hide');
            const hideEvent = new Event('mobilebar:hide');
            window.dispatchEvent(hideEvent);
            window.mobileBar.status = 'closed';
        }

        if (Math.abs(lastScrollTop - st) <= delta) {
            return;
        }

        lastScrollTop = st;
    }
});
</script>
